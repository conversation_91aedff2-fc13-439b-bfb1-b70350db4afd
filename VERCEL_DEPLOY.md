# Vercelデプロイガイド

## 🚀 Vercelでのデプロイ手順

### 1. Vercelアカウントの準備
1. [Vercel](https://vercel.com)にアクセス
2. GitHubアカウントでサインアップ/ログイン

### 2. プロジェクトのデプロイ
1. Vercelダッシュボードで「New Project」をクリック
2. GitHubリポジトリを選択
3. 「Deploy」をクリック

### 3. 環境変数の設定
Vercelダッシュボードの「Settings」→「Environment Variables」で以下を設定：

#### 必須環境変数
```
DATABASE_URL=libsql://your-database-url
DATABASE_TOKEN=your-database-token
LOGIN_URL=https://cmps-web.oka-pu.ac.jp/campusweb/ssologin.do?page=portal
TARGET_URL=https://cmps-web.oka-pu.ac.jp/campusweb/campussquare.do?_flowId=RSW0001000-flow
```

#### Tursoデータベースの設定
1. [Turso](https://turso.tech)でアカウント作成
2. 新しいデータベースを作成
3. データベースURLとトークンを取得
4. Vercelの環境変数に設定

### 4. デプロイ後の確認
- フロントエンド: `https://your-project.vercel.app`
- API: `https://your-project.vercel.app/api/scrape`

### 5. APIエンドポイント

#### GET /api/scrape
システム状態の確認
```bash
curl https://your-project.vercel.app/api/scrape
```

#### POST /api/scrape
スクレイピング実行
```bash
curl -X POST https://your-project.vercel.app/api/scrape \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "login_id=your_id&password=your_password"
```

### 6. トラブルシューティング

#### よくある問題
1. **環境変数が設定されていない**
   - Vercelダッシュボードで環境変数を確認
   - 再デプロイが必要な場合があります

2. **依存関係のエラー**
   - `requirements.txt`が正しく設定されているか確認
   - Python 3.9ランタイムを使用

3. **CORS エラー**
   - `vercel.json`でCORSヘッダーが設定済み
   - フロントエンドから正しいURLでAPIを呼び出しているか確認

### 7. セキュリティ注意事項
- 環境変数には機密情報を保存
- ログインIDとパスワードはHTTPS経由でのみ送信
- 本番環境では適切な認証機能の追加を推奨

## 📁 プロジェクト構造
```
OPUC2S/
├── api/
│   └── scrape.py          # Vercel Serverless Function
├── website/
│   ├── index.html         # フロントエンド
│   ├── script.js
│   └── style.css
├── vercel.json            # Vercel設定
├── requirements.txt       # Python依存関係
└── connection.py          # スクレイピングロジック
```

## 🔧 開発環境での確認
ローカルでVercel CLIを使用してテスト：
```bash
npm i -g vercel
vercel dev
```
