import json
import urllib.parse
import sys
import os

# プロジェクトルートをパスに追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from connection import login_and_scrape

def handler(request):
    # CORS ヘッダー
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Content-Type': 'application/json'
    }

    # OPTIONSリクエスト（プリフライト）
    if request.method == 'OPTIONS':
        return {
            'statusCode': 200,
            'headers': headers,
            'body': ''
        }

    # GETリクエスト
    if request.method == 'GET':
        # 環境変数の確認
        env_status = {
            'DATABASE_URL': 'set' if os.getenv('DATABASE_URL') else 'not set',
            'DATABASE_TOKEN': 'set' if os.getenv('DATABASE_TOKEN') else 'not set',
            'LOGIN_URL': 'set' if os.getenv('LOGIN_URL') else 'not set',
            'TARGET_URL': 'set' if os.getenv('TARGET_URL') else 'not set'
        }

        response = {
            'message': 'Campus Scraping API',
            'methods': ['POST'],
            'usage': 'POST with login_id and password',
            'environment': env_status
        }

        return {
            'statusCode': 200,
            'headers': headers,
            'body': json.dumps(response, ensure_ascii=False)
        }

    # POSTリクエスト
    if request.method == 'POST':
        try:
            # リクエストボディを取得
            body = request.body
            if hasattr(request, 'get_data'):
                body = request.get_data(as_text=True)
            elif hasattr(body, 'decode'):
                body = body.decode('utf-8')
            elif isinstance(body, str):
                body = body
            else:
                body = str(body)

            print(f"DEBUG: Request method: {request.method}")
            print(f"DEBUG: Request body type: {type(body)}")
            print(f"DEBUG: Request body: {body}")

            if not body:
                return {
                    'statusCode': 400,
                    'headers': headers,
                    'body': json.dumps({
                        'success': False,
                        'error': 'リクエストボディが空です'
                    }, ensure_ascii=False)
                }

            # フォームデータを解析
            try:
                form_data = urllib.parse.parse_qs(body)
                print(f"DEBUG: Parsed form data: {form_data}")
            except Exception as parse_error:
                print(f"DEBUG: Parse error: {parse_error}")
                return {
                    'statusCode': 400,
                    'headers': headers,
                    'body': json.dumps({
                        'success': False,
                        'error': f'データ解析エラー: {str(parse_error)}'
                    }, ensure_ascii=False)
                }

            login_id = form_data.get('login_id', [''])[0]
            password = form_data.get('password', [''])[0]

            print(f"DEBUG: login_id: '{login_id}'")
            print(f"DEBUG: password: {'***' if password else 'empty'}")

            if not login_id or not password:
                print(f"DEBUG: Missing login_id{login_id} or password{password}")
                return {
                    'statusCode': 400,
                    'headers': headers,
                    'body': json.dumps({
                        'success': False,
                        'error': 'ログインIDとパスワードは必須です'
                    }, ensure_ascii=False)
                }

            # スクレイピング実行
            result = login_and_scrape(
                user_id=login_id,
                password=password,
                extract_class="rishu-koma-inner"
            )

            if result:
                return {
                    'statusCode': 200,
                    'headers': headers,
                    'body': json.dumps({
                        'success': True,
                        'title': result.get('title', ''),
                        'extracted_count': len(result.get('extracted_elements', [])),
                        'db_saved': result.get('db_saved', False)
                    }, ensure_ascii=False)
                }
            else:
                return {
                    'statusCode': 500,
                    'headers': headers,
                    'body': json.dumps({
                        'success': False,
                        'error': 'スクレイピング処理に失敗しました'
                    }, ensure_ascii=False)
                }

        except Exception as e:
            print(f"DEBUG: Exception: {e}")
            return {
                'statusCode': 500,
                'headers': headers,
                'body': json.dumps({
                    'success': False,
                    'error': f'処理中にエラーが発生しました: {str(e)}'
                }, ensure_ascii=False)
            }

    # その他のメソッド
    return {
        'statusCode': 405,
        'headers': headers,
        'body': json.dumps({
            'success': False,
            'error': 'Method not allowed'
        })
    }
