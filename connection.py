import requests
from bs4 import BeautifulSoup
import datetime
import html
import json
import re
import logging
import os
from dotenv import load_dotenv
from turso_database import TursoDatabase
import asyncio

# .envファイルから環境変数を読み込み
load_dotenv()

# --- 1. ログ設定 ---
def setup_logging(log_level=logging.INFO, log_file=None, console_output=True):
    """
    ログ設定を初期化する

    Args:
        log_level: ログレベル (logging.DEBUG, INFO, WARNING, ERROR)
        log_file: ログファイル名 (Noneの場合はファイル出力なし)
        console_output: コンソール出力の有無 (False=ファイルのみ)
    """
    # ログフォーマットを設定
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # ルートロガーを取得
    logger = logging.getLogger()
    logger.setLevel(log_level)

    # 既存のハンドラーをクリア
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # コンソールハンドラーを追加（指定された場合のみ）
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    # ファイルハンドラーを追加（指定された場合）
    if log_file:
        # ログディレクトリを作成
        import os
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)

        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

# --- 2. 定数の設定 ---
# .envファイルから環境変数を読み込み
LOGIN_URL = os.getenv("LOGIN_URL")
TARGET_URL = os.getenv("TARGET_URL")

# デフォルトの認証情報（.envファイルから読み込み）
DEFAULT_USER_ID = os.getenv("DEFAULT_USER_ID")
DEFAULT_PASSWORD = os.getenv("DEFAULT_PASSWORD")

NUM_TO_WEEK = {
    '1':"月",
    '2':"火",
    '3':"水",
    '4':"木",
    '5':"金",
    '6':"土",
    '7':"日",
}





def extract_delete_call_params(html_content):
    """
    HTMLからDeleteCallAの最後の2つのパラメータを抽出する

    Args:
        html_content (str): HTML文字列

    Returns:
        tuple: (param1, param2) または None
    """
    # DeleteCallA('2025','0221','21C14601','5','1') のパターンを検索
    pattern = r"DeleteCallA\([^,]+,[^,]+,[^,]+,'([^']+)','([^']+)'\)"
    match = re.search(pattern, html_content)

    if match:
        return (match.group(1), match.group(2))
    return None


def parse_course_info(element):
    """
    履修要素から詳細情報を抽出する

    Args:
        element: BeautifulSoupの要素

    Returns:
        dict: 抽出された情報
    """
    info = {
        'tag': element.name,
        'classes': element.get('class', []),
        'text': element.get_text(strip=True)
    }

    # HTMLからDeleteCallAのパラメータを抽出
    html_str = str(element)
    params = extract_delete_call_params(html_str)

    if params:
        info['delete_params'] = {
            'param1': NUM_TO_WEEK[params[0]],
            'param2': params[1]
        }

    # テキストから科目情報を抽出
    text_lines = [line.strip() for line in element.get_text().split('\n') if line.strip()]

    if len(text_lines) >= 3 and text_lines[0] != "未登録":
        # 科目コード、科目名、教員名を抽出
        course_code = text_lines[0] if text_lines else ""
        course_name = text_lines[1] if len(text_lines) > 1 else ""
        instructor = text_lines[2] if len(text_lines) > 2 else ""

        info['course_info'] = {
            'course_code': course_code,
            'course_name': course_name,
            'instructor': instructor
        }

    return info


def login_to_campus(user_id, password):
    """
    キャンパスポータルにログインしてセッションを返す
    
    Args:
        user_id (str): ユーザーID
        password (str): パスワード
    
    Returns:
        requests.Session: ログイン済みのセッション、失敗時はNone
    """
    s = requests.Session()
    
    try:
        # Step 1: 初期ログインURLにアクセス
        initial_response = s.get(LOGIN_URL)
        
        # Step 2: SAML中間ページの処理
        soup = BeautifulSoup(initial_response.text, 'html.parser')
        form = soup.find('form')
        
        if not form:
            logging.error("❌ SAML中間フォームが見つかりません")
            return None
            
        
        # 隠しフィールドを全て取得
        form_data = {}
        for inp in form.find_all('input'):
            name = inp.get('name')
            value = inp.get('value', '')
            if name:
                form_data[name] = value
        
        # フォームのaction URLを取得
        action_url = form.get('action')
        if action_url.startswith('/'):
            base_url = f"https://{initial_response.url.split('/')[2]}"
            action_url = base_url + action_url
        
        
        # Step 3: SAML中間フォームを送信
        saml_response = s.post(action_url, data=form_data, allow_redirects=True, timeout=10)
        
        # Step 4: 実際のログインフォームを探す
        login_soup = BeautifulSoup(saml_response.text, 'html.parser')
        login_forms = login_soup.find_all('form')
        
        
        # ユーザー名・パスワード入力フォームを探す
        login_form = None
        for form in login_forms:
            inputs = form.find_all('input')
            has_username = any(inp.get('name', '').lower() in ['username', 'userid', 'login_id', 'j_username'] for inp in inputs)
            has_password = any(inp.get('type', '').lower() == 'password' for inp in inputs)
            
            if has_username and has_password:
                login_form = form
                break
        
        if not login_form:
            logging.error("❌ ログインフォームが見つかりません")
            return None
            
        
        # ログインデータを準備
        login_data = {}
        
        # inputフィールドを処理
        for inp in login_form.find_all('input'):
            name = inp.get('name')
            input_type = inp.get('type', '')
            value = inp.get('value', '')
            
            if name:
                if input_type.lower() == 'password':
                    login_data[name] = password
                elif name.lower() in ['username', 'userid', 'login_id', 'j_username']:
                    login_data[name] = user_id
                elif input_type.lower() in ['hidden', 'submit', 'checkbox']:
                    login_data[name] = value
                else:
                    login_data[name] = value
        
        # buttonフィールドも処理
        for btn in login_form.find_all('button'):
            name = btn.get('name')
            btn_type = btn.get('type', 'button')
            value = btn.get('value', '')
            
            
            if name and btn_type.lower() == 'submit':
                login_data[name] = value if value else ''
        
        # ログインフォームのaction URLを取得
        login_action = login_form.get('action')
        if login_action.startswith('/'):
            base_url = f"https://{saml_response.url.split('/')[2]}"
            login_action = base_url + login_action
        elif not login_action.startswith('http'):
            login_action = saml_response.url.rsplit('/', 1)[0] + '/' + login_action
        
        
        # Step 5: 実際のログインを実行
        
        # ヘッダーを追加してブラウザのように見せる
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ja,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': f"https://{saml_response.url.split('/')[2]}",
            'Referer': saml_response.url
        }
        
        login_response = s.post(login_action, data=login_data, headers=headers, allow_redirects=True, timeout=10)
        
        
        # 応答の最終URLをチェック
        if TARGET_URL in login_response.url or "campusportal" in login_response.url:
            return s
        elif "送信属性の選択" in login_response.text or "consent" in login_response.url.lower():
            
            # Step 6: 同意ページの処理
            consent_soup = BeautifulSoup(login_response.text, 'html.parser')
            consent_form = consent_soup.find('form')
            
            if not consent_form:
                return None
                
            # 同意フォームのデータを準備
            consent_data = {}
            
            # 隠しフィールドを取得
            for inp in consent_form.find_all('input', type='hidden'):
                name = inp.get('name')
                value = inp.get('value', '')
                if name:
                    consent_data[name] = value
            
            # デフォルトで選択されているラジオボタンを取得
            for inp in consent_form.find_all('input', type='radio'):
                if inp.get('checked') is not None:
                    name = inp.get('name')
                    value = inp.get('value', '')
                    consent_data[name] = value
            
            # 「同意」ボタンを押す
            consent_data['_eventId_proceed'] = '同意'
            
            # 同意フォームのaction URLを取得
            consent_action = consent_form.get('action')
            if consent_action.startswith('/'):
                base_url = f"https://{login_response.url.split('/')[2]}"
                consent_action = base_url + consent_action
            
            
            # Step 7: 同意フォームを送信
            consent_response = s.post(consent_action, data=consent_data, headers=headers, allow_redirects=True, timeout=10)

            
            # 最終的にキャンパスポータルに到達したかチェック
            if TARGET_URL in consent_response.url or "campusportal" in consent_response.url:
                return s
            
            else:
                # SAMLレスポンス自動送信ページかチェック
                if "document.forms[0].submit()" in consent_response.text:
                    
                    # SAMLレスポンスフォームを解析
                    saml_soup = BeautifulSoup(consent_response.text, 'html.parser')
                    saml_form = saml_soup.find('form')
                    
                    if not saml_form:
                        logging.error("❌ SAMLフォームが見つかりません")
                        return None
                        
                    # SAMLフォームのデータを準備
                    saml_data = {}
                    for inp in saml_form.find_all('input'):
                        name = inp.get('name')
                        value = inp.get('value', '')
                        if name:
                            saml_data[name] = value
                    
                    # SAMLフォームのaction URLを取得
                    saml_action = saml_form.get('action')
                    saml_action = html.unescape(saml_action)
                    
                    
                    # Step 8: SAMLレスポンスを送信
                    final_response = s.post(saml_action, data=saml_data, headers=headers, allow_redirects=True, timeout=10)

                    
                    # 最終的にキャンパスポータルに到達したかチェック
                    if TARGET_URL in final_response.url or "campusportal" in final_response.url:
                        return s
                    else:
                        logging.error("❌ SAML送信後もキャンパスポータルに到達できませんでした")
                        logging.info(f"最終URL: {final_response.url}")
                        return None
                else:
                    logging.error("❌ 同意後もキャンパスポータルに到達できませんでした")
                    logging.info(f"最終URL: {consent_response.url}")
                    return None
        else:
            logging.error("❌ ログイン失敗またはリダイレクトエラー")
            logging.info(f"最終アクセスURL: {login_response.url}")
            return None
            
    except Exception as e:
        logging.error(f"❌ ログイン中にエラーが発生しました: {e}")
        return None


def scrape_campus_data(session, user_id,target_url=None, extract_class=None):
    """
    ログイン済みセッションを使ってキャンパスデータをスクレイピングする
    
    Args:
        session (requests.Session): ログイン済みのセッション
        target_url (str, optional): 取得するURL。Noneの場合はTARGET_URLを使用
        extract_class (str, optional): 抽出するCSSクラス名
    
    Returns:
        dict: スクレイピング結果
    """
    if target_url is None:
        target_url = TARGET_URL
        
    try:
        target_response = session.get(target_url)
        
        # HTML解析
        soup = BeautifulSoup(target_response.text, 'html.parser')
        
        # ページタイトルを表示
        page_title = soup.title.string if soup.title else "タイトルタグなし"
        logging.info(f"取得したページのタイトル: {page_title}")
        
        # タイムスタンプを生成
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        result = {
            'title': page_title,
            'html_content': target_response.text,
            'timestamp': timestamp,
            'extracted_elements': []
        }
        
        # 特定のクラスの要素を抽出
        if extract_class:
            elements = soup.find_all(class_=extract_class)
            
            if elements:

                num = 0

                # resultにparse_course_infoのデータを適した形で追加
                
                for i, element in enumerate(elements):
                        
                        # 未登録の場合はjsonファイルに書き込まないものとする
                        if element.get_text(strip=True) == "未登録":
                            continue
                        
                        # dataが未登録でなければjsonに出力
                        num += 1

                        # 新しい解析機能を使用
                        parsed_info = parse_course_info(element)
                        
                        # JSONに保存する情報（HTMLは除外）
                        element_data = {
                            'index': num,
                            'text': parsed_info['text']
                        }

                        # DeleteCallAパラメータがあれば追加
                        if 'delete_params' in parsed_info:
                            element_data['delete_params'] = parsed_info['delete_params']

                        # 科目情報があれば追加
                        if 'course_info' in parsed_info:
                            element_data['course_info'] = parsed_info['course_info']

                        result['extracted_elements'].append(element_data)

                # JSONファイルとしても保存
                json_filename = f"indi_data/{user_id}.json"
                try:
                    json_data = {
                        'timestamp': timestamp,
                        'page_title': page_title,
                        'target_url': target_url,
                        'extract_class': extract_class,
                        'total_elements': len(elements),
                        'elements': result['extracted_elements']
                    }

                    with open(json_filename, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, ensure_ascii=False, indent=2)

                    logging.info(f"✅ JSON形式で保存しました: {json_filename}")
                    result['json_file'] = json_filename

                    # Tursoデータベースにも保存
                    try:
                        json_data['user_id'] = user_id  # ユーザーIDを追加

                        # 非同期でTursoデータベースに保存
                        async def save_to_turso():
                            turso_db = TursoDatabase()
                            try:
                                await turso_db.init_database()
                                success = await turso_db.save_json_data(json_data)
                                await turso_db.close()
                                return success
                            except Exception as e:
                                await turso_db.close()
                                raise e

                        turso_success = asyncio.run(save_to_turso())
                        result['db_saved'] = turso_success

                        if turso_success:
                            logging.info(f"✅ Tursoデータベースに保存しました: {len(result['extracted_elements'])}件の科目")
                        else:
                            logging.warning("⚠️ Tursoデータベース保存に失敗しました")



                    except Exception as db_error:
                        logging.error(f"❌ Tursoデータベース保存エラー: {db_error}")
                        logging.warning("⚠️ Tursoデータベース保存に失敗しました")
                        result['db_saved'] = False

                except Exception as e:
                    logging.error(f"❌ JSON保存エラー: {e}")
            else:
                logging.error(f"❌ {extract_class}クラスの要素が見つかりませんでした")
                
                # デバッグ用：利用可能なクラス名を表示
                all_classes = set()
                for elem in soup.find_all(class_=True):
                    all_classes.update(elem.get('class', []))
                
                related_classes = [cls for cls in all_classes if any(keyword in cls.lower() for keyword in extract_class.lower().split('-'))]
                if related_classes:
                    logging.info(f"関連しそうなクラス名: {related_classes}")
                else:
                    logging.info(f"関連するクラス名は見つかりませんでした")
                    logging.info(f"利用可能なクラス名の一部: {list(all_classes)[:20]}")
        
        return result
        
    except Exception as e:
        logging.error(f"❌ スクレイピング中にエラーが発生しました: {e}")
        return None


def save_data_as_json(data, filename=None):
    """
    データをJSON形式で保存する

    Args:
        data (dict): 保存するデータ
        filename (str, optional): ファイル名。Noneの場合は自動生成

    Returns:
        str: 保存されたファイル名
    """
    if filename is None:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"campus_data_{timestamp}.json"

    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logging.info(f"✅ JSON形式で保存しました: {filename}")
        return filename
    except Exception as e:
        logging.error(f"❌ JSON保存エラー: {e}")
        return None


def login_and_scrape(user_id=None, password=None, target_url=None, extract_class=None):
    """
    ログインとスクレイピングを一括で実行する便利関数

    Args:
        user_id (str): ユーザーID（必須）
        password (str): パスワード（必須）
        target_url (str, optional): 取得するURL。Noneの場合はTARGET_URLを使用
        extract_class (str, optional): 抽出するCSSクラス名

    Returns:
        dict: スクレイピング結果、またはエラー時はNone
    """

    # ユーザIDとパスワードの必須チェック
    if user_id is None or user_id.strip() == "":
        logging.error("❌ ユーザーIDが指定されていません")
        return None

    if password is None or password.strip() == "":
        logging.error("❌ パスワードが指定されていません")
        return None
        
    # ログイン
    session = login_to_campus(user_id, password)
    if session is None:
        logging.error("❌ ログインに失敗しました")
        return None
    
    # スクレイピング
    result = scrape_campus_data(session, user_id,target_url, extract_class)
    
    # セッションを閉じる
    session.close()
    
    return result


# メイン実行部分
if __name__ == "__main__":
         # デフォルトはコンソール出力なし

    # ログ設定を初期化
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f".log/mock.log"
    setup_logging(log_level=logging.INFO, log_file=log_file, console_output=False)

    logging.info("=== キャンパススクレイピング開始 ===")
    
    
    
    
    # =======main=======

    # デフォルト設定でログインとスクレイピングを実行
    result = login_and_scrape(extract_class="rishu-koma-inner")
    
    
    
    # =======log出力=======
    
    if result:
        logging.info(f"\n🎉 処理完了！")
        logging.info(f"タイトル: {result['title']}")
        if 'json_file' in result:
            logging.info(f"JSONファイル: {result['json_file']}")
            logging.info(f"抽出要素数: {len(result['extracted_elements'])}")
        logging.info(f"ログファイル: {log_file}")


    else:
        logging.error("❌ 処理に失敗しました")

    logging.info("=== キャンパススクレイピング終了 ===")
