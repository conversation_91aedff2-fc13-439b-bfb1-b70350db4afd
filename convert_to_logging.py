#!/usr/bin/env python3
"""
print文をlogging文に変換するスクリプト
"""

import re

def convert_prints_to_logging(file_path):
    """
    ファイル内のprint文をlogging文に変換する
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # print文のパターンを定義
    patterns = [
        # print(f"✅ ...") -> logging.info(f"✅ ...")
        (r'print\(f"✅([^"]*)"([^)]*)\)', r'logging.info(f"✅\1"\2)'),
        # print("✅ ...") -> logging.info("✅ ...")
        (r'print\("✅([^"]*)"([^)]*)\)', r'logging.info("✅\1"\2)'),
        
        # print(f"❌ ...") -> logging.error(f"❌ ...")
        (r'print\(f"❌([^"]*)"([^)]*)\)', r'logging.error(f"❌\1"\2)'),
        # print("❌ ...") -> logging.error("❌ ...")
        (r'print\("❌([^"]*)"([^)]*)\)', r'logging.error("❌\1"\2)'),
        
        # print(f"🎉 ...") -> logging.info(f"🎉 ...")
        (r'print\(f"🎉([^"]*)"([^)]*)\)', r'logging.info(f"🎉\1"\2)'),
        # print("🎉 ...") -> logging.info("🎉 ...")
        (r'print\("🎉([^"]*)"([^)]*)\)', r'logging.info("🎉\1"\2)'),
        
        # print(f"--- ...") -> logging.info(f"--- ...")
        (r'print\(f"---([^"]*)"([^)]*)\)', r'logging.info(f"---\1"\2)'),
        # print("--- ...") -> logging.info("--- ...")
        (r'print\("---([^"]*)"([^)]*)\)', r'logging.info("---\1"\2)'),
        
        # print(f"Step ...") -> logging.info(f"Step ...")
        (r'print\(f"Step([^"]*)"([^)]*)\)', r'logging.info(f"Step\1"\2)'),
        # print("Step ...") -> logging.info("Step ...")
        (r'print\("Step([^"]*)"([^)]*)\)', r'logging.info("Step\1"\2)'),
        
        # 一般的なf-string print -> logging.info
        (r'print\(f"([^"]*)"([^)]*)\)', r'logging.info(f"\1"\2)'),
        
        # 一般的な文字列 print -> logging.info
        (r'print\("([^"]*)"([^)]*)\)', r'logging.info("\1"\2)'),
        
        # その他のprint -> logging.info
        (r'print\(([^)]+)\)', r'logging.info(\1)'),
    ]
    
    # パターンを順番に適用
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    # ファイルに書き戻し
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {file_path} のprint文をlogging文に変換しました")

if __name__ == "__main__":
    convert_prints_to_logging("connection_refactored.py")
