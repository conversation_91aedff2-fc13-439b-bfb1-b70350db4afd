#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
table2.md構造からtable3.md構造への移行スクリプト
"""

import os
import logging
import asyncio
from typing import Dict, List
from dotenv import load_dotenv
import libsql_client

# .envファイルから環境変数を読み込み
load_dotenv()

class DatabaseMigrator:
    """データベース移行クラス"""
    
    def __init__(self):
        """
        データベース接続を初期化
        """
        self.database_url = os.getenv("DATABASE_URL")
        self.database_token = os.getenv("DATABASE_TOKEN")
        
        if not self.database_url or not self.database_token:
            raise ValueError("DATABASE_URLとDATABASE_TOKENが.envファイルに設定されていません")
        
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """データベースクライアントを初期化"""
        try:
            # HTTPSプロトコルを使用
            if self.database_url.startswith("libsql://"):
                https_url = self.database_url.replace("libsql://", "https://")
            else:
                https_url = self.database_url

            self.client = libsql_client.create_client(
                url=https_url,
                auth_token=self.database_token
            )
            
            logging.info("✅ データベースクライアントを初期化しました")
        except Exception as e:
            logging.error(f"❌ データベースクライアント初期化エラー: {e}")
            raise
    
    async def check_existing_tables(self) -> Dict[str, bool]:
        """既存テーブルの存在確認"""
        tables = {
            'instructor': False,
            'courses': False,
            'user': False,
            'attendance': False,
            'scraping_sessions': False
        }
        
        try:
            # SQLiteのテーブル一覧を取得
            result = await self.client.execute(
                "SELECT name FROM sqlite_master WHERE type='table'"
            )
            
            existing_tables = [row[0] for row in result.rows]
            
            for table in tables:
                tables[table] = table in existing_tables
            
            logging.info(f"既存テーブル: {existing_tables}")
            return tables
            
        except Exception as e:
            logging.error(f"❌ テーブル確認エラー: {e}")
            return tables
    
    async def backup_existing_data(self) -> Dict:
        """既存データのバックアップ"""
        backup_data = {}
        
        try:
            # instructorテーブルのバックアップ
            instructor_result = await self.client.execute("SELECT * FROM instructor")
            backup_data['instructor'] = [
                {
                    'instructor_id': row[0],
                    'instructor': row[1],
                    'created_at': row[2]
                }
                for row in instructor_result.rows
            ]
            
            # coursesテーブルのバックアップ（table2構造）
            try:
                courses_result = await self.client.execute("SELECT * FROM courses")
                backup_data['courses'] = [
                    {
                        'course_id': row[0],
                        'period': row[1],
                        'course_name': row[2],
                        'instructor_id': row[3],
                        'day_of_week': row[4],
                        'created_at': row[5]
                    }
                    for row in courses_result.rows
                ]
            except Exception:
                # table3構造の場合
                courses_result = await self.client.execute("SELECT * FROM courses")
                backup_data['courses'] = [
                    {
                        'course_id': row[0],
                        'course_name': row[1],
                        'instructor_id': row[2],
                        'period': row[3],
                        'day_of_week': row[4],
                        'length': row[5],
                        'created_at': row[6]
                    }
                    for row in courses_result.rows
                ]
            
            # userテーブルのバックアップ
            try:
                user_result = await self.client.execute("SELECT * FROM user")
                # table2構造の場合
                backup_data['user'] = [
                    {
                        'id': row[0],
                        'login_id': row[1],
                        'course_id': row[2] if len(row) > 2 else None,
                        'created_at': row[3] if len(row) > 3 else row[2]
                    }
                    for row in user_result.rows
                ]
            except Exception:
                # table3構造の場合
                user_result = await self.client.execute("SELECT * FROM user")
                backup_data['user'] = [
                    {
                        'user_id': row[0],
                        'login_id': row[1],
                        'created_at': row[2]
                    }
                    for row in user_result.rows
                ]
            
            # attendanceテーブルのバックアップ（存在する場合）
            try:
                attendance_result = await self.client.execute("SELECT * FROM attendance")
                backup_data['attendance'] = [
                    {
                        'attendance_id': row[0],
                        'user_id': row[1],
                        'course_id': row[2],
                        'period': row[3],
                        'created_at': row[4]
                    }
                    for row in attendance_result.rows
                ]
            except Exception:
                backup_data['attendance'] = []
            
            # scraping_sessionsテーブルのバックアップ
            try:
                sessions_result = await self.client.execute("SELECT * FROM scraping_sessions")
                backup_data['scraping_sessions'] = [
                    {
                        'id': row[0],
                        'user_id': row[1],
                        'timestamp': row[2],
                        'page_title': row[3],
                        'target_url': row[4],
                        'extract_class': row[5],
                        'total_elements': row[6],
                        'created_at': row[7]
                    }
                    for row in sessions_result.rows
                ]
            except Exception:
                backup_data['scraping_sessions'] = []
            
            logging.info(f"✅ データバックアップ完了: {len(backup_data)}テーブル")
            return backup_data
            
        except Exception as e:
            logging.error(f"❌ データバックアップエラー: {e}")
            return {}
    
    async def drop_old_tables(self):
        """古いテーブルを削除"""
        try:
            await self.client.execute("DROP TABLE IF EXISTS user")
            await self.client.execute("DROP TABLE IF EXISTS courses")
            await self.client.execute("DROP TABLE IF EXISTS attendance")
            logging.info("✅ 古いテーブルを削除しました")
        except Exception as e:
            logging.error(f"❌ テーブル削除エラー: {e}")
            raise
    
    async def create_new_tables(self):
        """新しいテーブル構造を作成"""
        try:
            # 講師テーブル（変更なし）
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS instructor (
                    instructor_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    instructor TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 科目テーブル（table3.md構造）
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS courses (
                    course_id TEXT PRIMARY KEY,
                    course_name TEXT NOT NULL,
                    instructor_id INTEGER NOT NULL,
                    period INTEGER NOT NULL,
                    day_of_week TEXT NOT NULL,
                    length INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (instructor_id) REFERENCES instructor(instructor_id)
                )
            """)

            # ユーザーテーブル（table3.md構造）
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS user (
                    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    login_id TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 受講テーブル（table3.md構造）
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS attendance (
                    attendance_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    course_id TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user(user_id),
                    FOREIGN KEY (course_id) REFERENCES courses(course_id)
                )
            """)

            logging.info("✅ 新しいテーブル構造を作成しました")
        except Exception as e:
            logging.error(f"❌ テーブル作成エラー: {e}")
            raise
    
    async def migrate_data(self, backup_data: Dict):
        """データを新しい構造に移行"""
        try:
            # 講師データの移行（変更なし）
            for instructor in backup_data.get('instructor', []):
                await self.client.execute(
                    "INSERT INTO instructor (instructor_id, instructor, created_at) VALUES (?, ?, ?)",
                    [instructor['instructor_id'], instructor['instructor'], instructor['created_at']]
                )
            
            # ユーザーデータの移行
            user_mapping = {}  # 古いIDと新しいIDのマッピング
            for user in backup_data.get('user', []):
                result = await self.client.execute(
                    "INSERT INTO user (login_id, created_at) VALUES (?, ?)",
                    [user['login_id'], user.get('created_at')]
                )
                user_mapping[user.get('id', user.get('user_id'))] = result.last_insert_rowid
            
            # 科目データの統合と移行
            course_groups = self._group_courses_for_migration(backup_data.get('courses', []))
            
            for course_data in course_groups:
                await self.client.execute(
                    """
                    INSERT INTO courses 
                    (course_id, course_name, instructor_id, period, day_of_week, length, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    """,
                    [
                        course_data['course_id'],
                        course_data['course_name'],
                        course_data['instructor_id'],
                        course_data['period'],
                        course_data['day_of_week'],
                        course_data['length'],
                        course_data['created_at']
                    ]
                )
            
            # 受講データの移行
            for user in backup_data.get('user', []):
                if 'course_id' in user and user['course_id']:
                    # table2構造からの移行
                    old_user_id = user.get('id', user.get('user_id'))
                    new_user_id = user_mapping.get(old_user_id)

                    if new_user_id:
                        # 科目ごとに1つの受講レコードを作成
                        unique_courses = set()
                        for course in backup_data.get('courses', []):
                            if course['course_id'] == user['course_id']:
                                course_key = f"{course['course_id']}_{course['course_name']}_{course['instructor_id']}"
                                if course_key not in unique_courses:
                                    unique_courses.add(course_key)
                                    await self.client.execute(
                                        "INSERT INTO attendance (user_id, course_id, created_at) VALUES (?, ?, ?)",
                                        [new_user_id, course['course_id'], user.get('created_at')]
                                    )
            
            logging.info("✅ データ移行完了")
        except Exception as e:
            logging.error(f"❌ データ移行エラー: {e}")
            raise
    
    def _group_courses_for_migration(self, courses: List[Dict]) -> List[Dict]:
        """科目データを統合（table2からtable3への移行用）"""
        course_groups = {}
        
        for course in courses:
            # table2構造の場合
            if 'period' in course and isinstance(course['period'], str):
                key = f"{course['course_name']}_{course['instructor_id']}_{course['day_of_week']}"
                
                if key not in course_groups:
                    course_groups[key] = {
                        'course_id': f"{course['course_id']}_{course['period']}",
                        'course_name': course['course_name'],
                        'instructor_id': course['instructor_id'],
                        'day_of_week': course['day_of_week'],
                        'periods': [],
                        'created_at': course['created_at']
                    }
                
                try:
                    period = int(course['period'])
                    course_groups[key]['periods'].append(period)
                except ValueError:
                    continue
        
        # 連続する時間を統合
        result = []
        for group_data in course_groups.values():
            periods = sorted(group_data['periods'])
            if not periods:
                continue
            
            # 最初の時限と長さを計算
            start_period = periods[0]
            length = len(periods)
            
            result.append({
                'course_id': f"{group_data['course_id']}_{start_period}",
                'course_name': group_data['course_name'],
                'instructor_id': group_data['instructor_id'],
                'period': start_period,
                'day_of_week': group_data['day_of_week'],
                'length': length,
                'created_at': group_data['created_at']
            })
        
        return result
    
    async def close(self):
        """データベース接続を閉じる"""
        if self.client:
            await self.client.close()
            logging.info("✅ データベース接続を閉じました")


async def main():
    """メイン実行関数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('.log/migration.log', encoding='utf-8')
        ]
    )
    
    migrator = DatabaseMigrator()
    
    try:
        logging.info("=== データベース移行開始 ===")
        
        # 1. 既存テーブルの確認
        existing_tables = await migrator.check_existing_tables()
        logging.info(f"既存テーブル状況: {existing_tables}")
        
        # 2. データのバックアップ
        backup_data = await migrator.backup_existing_data()
        
        # 3. 古いテーブルの削除
        await migrator.drop_old_tables()
        
        # 4. 新しいテーブルの作成
        await migrator.create_new_tables()
        
        # 5. データの移行
        await migrator.migrate_data(backup_data)
        
        logging.info("=== データベース移行完了 ===")
        
    except Exception as e:
        logging.error(f"❌ 移行エラー: {e}")
        raise
    finally:
        await migrator.close()


if __name__ == "__main__":
    asyncio.run(main())
