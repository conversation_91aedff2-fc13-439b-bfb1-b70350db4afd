#!/usr/bin/env python3
"""
簡単なローカルテストサーバー
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import os
import urllib.parse
import json
import sys

# プロジェクトルートをパスに追加
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            # HTMLファイルを返す
            try:
                with open('website/index.html', 'r', encoding='utf-8') as f:
                    content = f.read()
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(content.encode('utf-8'))
            except FileNotFoundError:
                self.send_error(404, 'File not found')
        
        elif self.path == '/style.css':
            try:
                with open('website/style.css', 'r', encoding='utf-8') as f:
                    content = f.read()
                self.send_response(200)
                self.send_header('Content-Type', 'text/css')
                self.end_headers()
                self.wfile.write(content.encode('utf-8'))
            except FileNotFoundError:
                self.send_error(404, 'File not found')
        
        elif self.path == '/script.js':
            try:
                with open('website/script.js', 'r', encoding='utf-8') as f:
                    content = f.read()
                    # API_ENDPOINTをローカル用に変更
                    content = content.replace("const API_ENDPOINT = '/api/scrape';", 
                                            "const API_ENDPOINT = '/api/scrape';")
                self.send_response(200)
                self.send_header('Content-Type', 'application/javascript')
                self.end_headers()
                self.wfile.write(content.encode('utf-8'))
            except FileNotFoundError:
                self.send_error(404, 'File not found')
        
        elif self.path == '/api/scrape':
            # API情報を返す
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            # 環境変数の確認
            env_status = {
                'DATABASE_URL': 'set' if os.getenv('DATABASE_URL') else 'not set',
                'DATABASE_TOKEN': 'set' if os.getenv('DATABASE_TOKEN') else 'not set',
                'LOGIN_URL': 'set' if os.getenv('LOGIN_URL') else 'not set',
                'TARGET_URL': 'set' if os.getenv('TARGET_URL') else 'not set'
            }
            
            response = {
                'message': 'Campus Scraping API (Local Test)',
                'methods': ['POST'],
                'usage': 'POST with login_id and password',
                'environment': env_status
            }
            
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        
        else:
            self.send_error(404, 'Not found')
    
    def do_POST(self):
        print(f"\n=== POST Request Debug ===")
        print(f"Path: {self.path}")
        print(f"Headers: {dict(self.headers)}")

        if self.path == '/api/scrape':
            try:
                # Content-Lengthを取得
                content_length = int(self.headers.get('Content-Length', 0))
                print(f"Content-Length: {content_length}")

                if content_length == 0:
                    print("ERROR: Content-Length is 0")
                    self.send_error_response(400, 'リクエストボディが空です')
                    return

                # POSTデータを読み取り
                post_data = self.rfile.read(content_length)
                print(f"Raw POST data: {post_data}")

                # URLエンコード形式のみサポート
                try:
                    form_data = urllib.parse.parse_qs(post_data.decode('utf-8'))
                    print(f"Parsed form data: {form_data}")
                except Exception as parse_error:
                    print(f"Parse error: {parse_error}")
                    self.send_error_response(400, f'データ解析エラー: {str(parse_error)}')
                    return

                login_id = form_data.get('login_id', [''])[0]
                password = form_data.get('password', [''])[0]

                print(f"login_id: '{login_id}'")
                print(f"password: {'***' if password else 'empty'}")

                if not login_id or not password:
                    print("ERROR: Missing login_id or password")
                    self.send_error_response(400, 'ログインIDとパスワードは必須です')
                    return
                
                # 実際のスクレイピングは行わず、テスト用レスポンスを返す
                self.send_success_response({
                    'success': True,
                    'title': 'テスト用ページタイトル',
                    'extracted_count': 5,
                    'db_saved': True,
                    'message': 'ローカルテスト用のダミーレスポンスです'
                })
                
            except Exception as e:
                print(f"エラー: {e}")
                self.send_error_response(500, f'処理中にエラーが発生しました: {str(e)}')
        else:
            self.send_error(404, 'Not found')
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.end_headers()
    
    def send_success_response(self, data):
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))

    def send_error_response(self, status_code, message):
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_response = {
            'success': False,
            'error': message
        }
        
        self.wfile.write(json.dumps(error_response, ensure_ascii=False).encode('utf-8'))

if __name__ == '__main__':
    port = 8000
    server = HTTPServer(('localhost', port), TestHandler)
    print(f"🌐 ローカルテストサーバーを起動しました")
    print(f"📱 ブラウザで http://localhost:{port} にアクセスしてください")
    print("🛑 停止するには Ctrl+C を押してください")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n✅ サーバーを停止しました")
        server.shutdown()
