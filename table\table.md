# table

## instruct

| 列名   |
| ---- |
| 説明   |
| 制約   |
| 外部キー |

UQ : Unique

NN : Non Null

PK : Primary Key

SN : Serial Number



## table construct

- courses

| course_id   | course_name | instructor_id  | day_of_week | period | created_at                |
| ----------- | ----------- | -------------- | ----------- | ------ | ------------------------- |
| 科目コード       | 科目名         | 講師id           | 曜日          | 時間     | 作成時間                      |
| PK,UQ.NN,SN | NN          | NN             | NN          | NN     | DEFAULT CURRENT_TIMESTAMP |
| --          | --          | instructor(id) | --          | --     | --                        |

- instructor

| instructor_id | instructor | created_at                |
| ------------- | ---------- | ------------------------- |
| 講師id          | 講師名        | 作成日時                      |
| PK.UQ,NN.SN   | NN         | DEFAULT CURRENT_TIMESTAMP |
| --            | --         | --                        |

- user

| id          | login_id | course_id          | created_at        |
| ----------- | -------- | ------------------ | ----------------- |
| ユーザid       | login id | 科目コード              | 作成日時              |
| PK.UQ,NN.SN | NN       | NN                 | CURRENT_TIMESTAMP |
| --          | --       | courses(course_id) | --                |




