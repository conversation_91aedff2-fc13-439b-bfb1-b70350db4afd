# table

## instruct

| 列名   |
| ---- |
| 説明   |
| 制約   |
| 外部キー |

UQ : Unique

NN : Non Null

PK : Primary Key

SN : Serial Number



## table construct

- courses

SELECT * FROM courses WHERE course_name == ? AND instructor_id == ?;

| course_id  | course_name | instructor_id  | period | day_of_week | length           | created_at                |
| ---------- | ----------- | -------------- | ------ | ----------- | ---------------- | ------------------------- |
| 科目コード | 科目名      | 講師id         | 時間目 | 曜日        | 授業の長さ(時間) | 作成時間                  |
| PK         | NN          | NN             | NN     | NN          | NN               | DEFAULT CURRENT_TIMESTAMP |
| --         | --          | instructor(id) | --     | --          | --               | --                        |

補足:lengthとは講義のコマ数のことであり、例えばcourse_id:1,period:3,day_of_week:金,length:3であるとき、金曜の3,4,5限目にcourse_id:1の授業が入っているものとする。
元データはこれらは複数のデータとして保存されているはずである。
例>
course_id:1,period:3,day_of_week:金
course_id:1,period:4,day_of_week:金
course_id:1,period:5,day_of_week:金
そのため、一つにまとめる必要がある。

- instructor

| instructor_id | instructor | created_at                |
| ------------- | ---------- | ------------------------- |
| 講師id          | 講師名        | 作成日時                      |
| PK.UQ,NN.SN   | NN         | DEFAULT CURRENT_TIMESTAMP |
| --            | --         | --                        |

- attendance

| attendance_id | user_id | course_id          | created_at        |
| ----------- | -------- | ------------------ | ----------------- |
| 受講ID     | ユーザID | 科目コード              | 作成日時              |
| PK.UQ,NN.SN | NN | NN                 | DEFAULT CURRENT_TIMESTAMP |
| --          | user(id) | courses(course_id) | --                |

- user

| user_id     | login_id | created_at                |
| ----------- | -------- | ------------------------- |
| ユーザid    | login id | 作成日時                  |
| PK.UQ,NN.SN | NN       | DEFAULT CURRENT_TIMESTAMP |
| --          | --       | --                        |
