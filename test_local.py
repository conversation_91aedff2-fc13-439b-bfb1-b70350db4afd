#!/usr/bin/env python3
"""
ローカルテスト用スクリプト
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.scrape import handler
from http.server import HTTPServer
import urllib.parse

class MockRequest:
    def __init__(self, method='GET', headers=None, body=b''):
        self.method = method
        self.headers = headers or {}
        self.body = body
    
    def get_data(self, as_text=False):
        if as_text:
            return self.body.decode('utf-8')
        return self.body

class TestHandler:
    def __init__(self):
        self.response_code = None
        self.response_headers = {}
        self.response_body = b''
    
    def send_response(self, code):
        self.response_code = code
    
    def send_header(self, name, value):
        self.response_headers[name] = value
    
    def end_headers(self):
        pass
    
    def wfile_write(self, data):
        self.response_body += data

def test_get():
    print("=== GET リクエストテスト ===")
    test_handler = TestHandler()
    test_handler.headers = {}
    test_handler.wfile = type('MockFile', (), {'write': test_handler.wfile_write})()
    
    # handlerクラスのインスタンスを作成
    h = handler()
    h.send_response = test_handler.send_response
    h.send_header = test_handler.send_header
    h.end_headers = test_handler.end_headers
    h.wfile = test_handler.wfile
    h.headers = {}
    
    h.do_GET()
    
    print(f"レスポンスコード: {test_handler.response_code}")
    print(f"ヘッダー: {test_handler.response_headers}")
    print(f"ボディ: {test_handler.response_body.decode('utf-8')}")

def test_post():
    print("\n=== POST リクエストテスト ===")
    test_handler = TestHandler()
    
    # テストデータ
    form_data = "login_id=test123&password=testpass"
    
    test_handler.headers = {
        'Content-Length': str(len(form_data)),
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    # handlerクラスのインスタンスを作成
    h = handler()
    h.send_response = test_handler.send_response
    h.send_header = test_handler.send_header
    h.end_headers = test_handler.end_headers
    h.wfile = type('MockFile', (), {'write': test_handler.wfile_write})()
    h.headers = test_handler.headers
    h.rfile = type('MockFile', (), {'read': lambda x: form_data.encode('utf-8')})()
    h.send_error_response = lambda code, msg: print(f"エラー: {code} - {msg}")
    h.send_success_response = lambda data: print(f"成功: {data}")
    
    try:
        h.do_POST()
    except Exception as e:
        print(f"エラーが発生しました: {e}")
    
    print(f"レスポンスコード: {test_handler.response_code}")
    print(f"ヘッダー: {test_handler.response_headers}")
    print(f"ボディ: {test_handler.response_body.decode('utf-8')}")

if __name__ == "__main__":
    print("🧪 ローカルテストを開始します...")
    
    # 環境変数の確認
    print("\n=== 環境変数確認 ===")
    env_vars = ['DATABASE_URL', 'DATABASE_TOKEN', 'LOGIN_URL', 'TARGET_URL']
    for var in env_vars:
        value = os.getenv(var)
        print(f"{var}: {'設定済み' if value else '未設定'}")
    
    test_get()
    test_post()
    
    print("\n✅ テスト完了")
