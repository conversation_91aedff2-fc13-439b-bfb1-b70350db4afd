#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
table3.md構造のテストスクリプト
"""

import os
import logging
import asyncio
from dotenv import load_dotenv
from turso_database import TursoDatabase

# .envファイルから環境変数を読み込み
load_dotenv()

async def test_database_operations():
    """データベース操作のテスト"""
    
    # ログ設定
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    db = TursoDatabase()
    
    try:
        logging.info("=== table3.md構造テスト開始 ===")
        
        # 1. データベース初期化テスト
        logging.info("1. データベース初期化テスト")
        await db.init_database()
        logging.info("✅ データベース初期化成功")
        
        # 2. テストデータの作成
        logging.info("2. テストデータ保存テスト")
        test_data = {
            'user_id': 'test_user_001',
            'timestamp': '20241229_120000',
            'page_title': 'テストページ',
            'target_url': 'https://test.example.com',
            'extract_class': 'test-class',
            'total_elements': 3,
            'elements': [
                {
                    'course_info': {
                        'course_code': 'CS101',
                        'course_name': 'コンピュータサイエンス入門',
                        'instructor': '田中太郎'
                    },
                    'delete_params': {
                        'param1': '月',  # 曜日
                        'param2': '1'    # 時限
                    }
                },
                {
                    'course_info': {
                        'course_code': 'CS101',
                        'course_name': 'コンピュータサイエンス入門',
                        'instructor': '田中太郎'
                    },
                    'delete_params': {
                        'param1': '月',  # 曜日
                        'param2': '2'    # 時限
                    }
                },
                {
                    'course_info': {
                        'course_code': 'MATH201',
                        'course_name': '線形代数',
                        'instructor': '佐藤花子'
                    },
                    'delete_params': {
                        'param1': '水',  # 曜日
                        'param2': '3'    # 時限
                    }
                }
            ]
        }
        
        success = await db.save_json_data(test_data)
        if success:
            logging.info("✅ テストデータ保存成功")
        else:
            logging.error("❌ テストデータ保存失敗")
            return False
        
        # 3. データ取得テスト
        logging.info("3. データ取得テスト")
        courses = await db.get_latest_courses('test_user_001')
        logging.info(f"取得した科目数: {len(courses)}")
        
        for course in courses:
            logging.info(f"科目: {course}")
        
        # 4. 統計情報取得テスト
        logging.info("4. 統計情報取得テスト")
        stats = await db.get_database_stats()
        logging.info(f"統計情報: {stats}")
        
        # 5. データ構造の確認
        logging.info("5. データ構造確認テスト")
        
        # coursesテーブルの確認
        courses_result = await db.client.execute("SELECT * FROM courses")
        logging.info(f"coursesテーブル: {len(courses_result.rows)}件")
        for row in courses_result.rows:
            logging.info(f"  科目: {row}")
        
        # attendanceテーブルの確認
        attendance_result = await db.client.execute("SELECT * FROM attendance")
        logging.info(f"attendanceテーブル: {len(attendance_result.rows)}件")
        for row in attendance_result.rows:
            logging.info(f"  受講: {row}")
        
        # userテーブルの確認
        user_result = await db.client.execute("SELECT * FROM user")
        logging.info(f"userテーブル: {len(user_result.rows)}件")
        for row in user_result.rows:
            logging.info(f"  ユーザー: {row}")
        
        # instructorテーブルの確認
        instructor_result = await db.client.execute("SELECT * FROM instructor")
        logging.info(f"instructorテーブル: {len(instructor_result.rows)}件")
        for row in instructor_result.rows:
            logging.info(f"  講師: {row}")
        
        logging.info("=== table3.md構造テスト完了 ===")
        return True
        
    except Exception as e:
        logging.error(f"❌ テストエラー: {e}")
        return False
    finally:
        await db.close()


async def test_course_grouping():
    """科目統合機能のテスト"""
    logging.info("=== 科目統合機能テスト ===")
    
    db = TursoDatabase()
    
    # テストデータ（連続する時間の授業）
    test_elements = [
        {
            'course_info': {
                'course_code': 'PHYS301',
                'course_name': '物理学実験',
                'instructor': '山田一郎'
            },
            'delete_params': {
                'param1': '金',
                'param2': '3'
            }
        },
        {
            'course_info': {
                'course_code': 'PHYS301',
                'course_name': '物理学実験',
                'instructor': '山田一郎'
            },
            'delete_params': {
                'param1': '金',
                'param2': '4'
            }
        },
        {
            'course_info': {
                'course_code': 'PHYS301',
                'course_name': '物理学実験',
                'instructor': '山田一郎'
            },
            'delete_params': {
                'param1': '金',
                'param2': '5'
            }
        }
    ]
    
    # 科目統合のテスト
    grouped_courses = db._group_courses_by_subject(test_elements)
    
    logging.info(f"統合前: {len(test_elements)}件")
    logging.info(f"統合後: {len(grouped_courses)}件")
    
    for course in grouped_courses:
        logging.info(f"統合科目: {course}")
    
    # 期待値の確認
    if len(grouped_courses) == 1:
        course = grouped_courses[0]
        if (course['course_name'] == '物理学実験' and 
            course['start_period'] == 3 and 
            course['length'] == 3):
            logging.info("✅ 科目統合機能テスト成功")
            return True
    
    logging.error("❌ 科目統合機能テスト失敗")
    return False


async def main():
    """メイン実行関数"""
    try:
        # 基本的なデータベース操作テスト
        test1_result = await test_database_operations()
        
        # 科目統合機能テスト
        test2_result = await test_course_grouping()
        
        if test1_result and test2_result:
            print("\n🎉 全てのテストが成功しました！")
            print("table3.md構造への移行が正常に完了しています。")
        else:
            print("\n❌ テストに失敗しました。")
            print("コードを確認してください。")
            
    except Exception as e:
        logging.error(f"❌ メインテストエラー: {e}")


if __name__ == "__main__":
    asyncio.run(main())
