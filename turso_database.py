#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Turso/LibSQLデータベース操作モジュール（シンプル版）
元の動作していた状態に戻したバージョン
"""

import os
import json
import logging
import asyncio
from typing import Dict, List, Optional
from datetime import datetime
from dotenv import load_dotenv
import libsql_client

# .envファイルから環境変数を読み込み
load_dotenv()


class TursoDatabase:
    """Turso/LibSQLデータベース操作クラス（シンプル版）"""
    
    def __init__(self):
        """
        Tursoデータベース接続を初期化
        """
        self.database_url = os.getenv("DATABASE_URL")
        self.database_token = os.getenv("DATABASE_TOKEN")
        
        if not self.database_url or not self.database_token:
            raise ValueError("DATABASE_URLとDATABASE_TOKENが.envファイルに設定されていません")
        
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Tursoクライアントを初期化"""
        try:
            # HTTPSプロトコルを使用
            if self.database_url.startswith("libsql://"):
                https_url = self.database_url.replace("libsql://", "https://")
            else:
                https_url = self.database_url

            self.client = libsql_client.create_client(
                url=https_url,
                auth_token=self.database_token
            )
            
            logging.info("✅ Tursoデータベースクライアントを初期化しました")
        except Exception as e:
            logging.error(f"❌ Tursoクライアント初期化エラー: {e}")
            raise
    
    async def init_database(self):
        """
        データベーステーブルを初期化（table3.md構造版）
        """
        try:
            # 講師テーブル（instructor）
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS instructor (
                    instructor_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    instructor TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 科目テーブル（courses）- table3.md構造
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS courses (
                    course_id TEXT PRIMARY KEY,
                    course_name TEXT NOT NULL,
                    instructor_id INTEGER NOT NULL,
                    period INTEGER NOT NULL,
                    day_of_week TEXT NOT NULL,
                    length INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (instructor_id) REFERENCES instructor(instructor_id)
                )
            """)

            # ユーザーテーブル（user）- table3.md構造
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS user (
                    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    login_id TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 受講テーブル（attendance）- table3.md構造
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS attendance (
                    attendance_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    course_id TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user(user_id),
                    FOREIGN KEY (course_id) REFERENCES courses(course_id)
                )
            """)

            # セッション履歴テーブル（スクレイピング履歴用）
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS scraping_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    page_title TEXT,
                    target_url TEXT,
                    extract_class TEXT,
                    total_elements INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            logging.info("✅ Tursoデータベーステーブルを初期化しました（table3.md構造）")
        except Exception as e:
            logging.error(f"❌ Tursoデータベース初期化エラー: {e}")
            raise
    
    def _group_courses_by_subject(self, elements: List[Dict]) -> List[Dict]:
        """
        科目データを統合して連続する時間の授業を1つにまとめる

        Args:
            elements: スクレイピングした要素のリスト

        Returns:
            統合された科目データのリスト
        """
        course_groups = {}

        for element in elements:
            course_info = element.get('course_info', {})
            delete_params = element.get('delete_params', {})

            course_code = course_info.get('course_code', '').strip()
            course_name = course_info.get('course_name', '').strip()
            instructor_name = course_info.get('instructor', '').strip()
            day_of_week = delete_params.get('param1', '').strip()
            period_str = delete_params.get('param2', '').strip()

            if not all([course_code, course_name, instructor_name, day_of_week, period_str]):
                continue

            try:
                period = int(period_str)
            except ValueError:
                continue

            # 科目の一意キー（科目名+講師+曜日）
            key = f"{course_name}_{instructor_name}_{day_of_week}"

            if key not in course_groups:
                course_groups[key] = {
                    'course_code': course_code,
                    'course_name': course_name,
                    'instructor': instructor_name,
                    'day_of_week': day_of_week,
                    'periods': []
                }

            course_groups[key]['periods'].append(period)

        # 連続する時間を統合
        result = []
        for group_data in course_groups.values():
            periods = sorted(group_data['periods'])
            if not periods:
                continue

            # 連続する時間をグループ化
            consecutive_groups = []
            current_group = [periods[0]]

            for i in range(1, len(periods)):
                if periods[i] == periods[i-1] + 1:
                    current_group.append(periods[i])
                else:
                    consecutive_groups.append(current_group)
                    current_group = [periods[i]]
            consecutive_groups.append(current_group)

            # 各連続グループを1つの科目として登録
            for group in consecutive_groups:
                result.append({
                    'course_code': f"{group_data['course_code']}_{group[0]}",  # 開始時限を含む一意ID
                    'course_name': group_data['course_name'],
                    'instructor': group_data['instructor'],
                    'day_of_week': group_data['day_of_week'],
                    'start_period': group[0],
                    'length': len(group)
                })

        return result

    async def _get_or_create_user(self, login_id: str) -> int:
        """ユーザーを取得または作成"""
        result = await self.client.execute(
            "SELECT user_id FROM user WHERE login_id = ?",
            [login_id]
        )

        if result.rows:
            return result.rows[0][0]
        else:
            new_user = await self.client.execute(
                "INSERT INTO user (login_id) VALUES (?)",
                [login_id]
            )
            return new_user.last_insert_rowid

    async def _get_or_create_instructor(self, instructor_name: str) -> int:
        """講師を取得または作成"""
        result = await self.client.execute(
            "SELECT instructor_id FROM instructor WHERE instructor = ?",
            [instructor_name]
        )

        if result.rows:
            return result.rows[0][0]
        else:
            new_instructor = await self.client.execute(
                "INSERT INTO instructor (instructor) VALUES (?)",
                [instructor_name]
            )
            return new_instructor.last_insert_rowid

    async def _insert_or_update_course(self, course_id: str, course_name: str,
                                     instructor_id: int, period: int,
                                     day_of_week: str, length: int):
        """科目を挿入または更新"""
        result = await self.client.execute(
            "SELECT course_id FROM courses WHERE course_id = ?",
            [course_id]
        )

        if result.rows:
            await self.client.execute(
                """
                UPDATE courses SET
                course_name = ?, instructor_id = ?, period = ?,
                day_of_week = ?, length = ?
                WHERE course_id = ?
                """,
                [course_name, instructor_id, period, day_of_week, length, course_id]
            )
        else:
            await self.client.execute(
                """
                INSERT INTO courses
                (course_id, course_name, instructor_id, period, day_of_week, length)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                [course_id, course_name, instructor_id, period, day_of_week, length]
            )

    async def _insert_attendance(self, user_id: int, course_id: str):
        """受講情報を挿入"""
        result = await self.client.execute(
            "SELECT attendance_id FROM attendance WHERE user_id = ? AND course_id = ?",
            [user_id, course_id]
        )

        if not result.rows:
            await self.client.execute(
                "INSERT INTO attendance (user_id, course_id) VALUES (?, ?)",
                [user_id, course_id]
            )

    async def save_json_data(self, json_data: Dict) -> bool:
        """
        JSONデータをTursoデータベースに保存（table3.md構造版）

        Args:
            json_data (Dict): 保存するJSONデータ

        Returns:
            bool: 保存成功時True
        """
        try:
            # セッション情報を挿入
            await self.client.execute(
                """
                INSERT INTO scraping_sessions
                (user_id, timestamp, page_title, target_url, extract_class, total_elements)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                [
                    json_data.get('user_id', 'unknown'),
                    json_data.get('timestamp'),
                    json_data.get('page_title'),
                    json_data.get('target_url'),
                    json_data.get('extract_class'),
                    json_data.get('total_elements', 0)
                ]
            )

            user_id = json_data.get('user_id', 'unknown')
            elements = json_data.get('elements', [])

            # ユーザーを挿入または取得
            db_user_id = await self._get_or_create_user(user_id)

            # 科目データを統合処理
            course_groups = self._group_courses_by_subject(elements)

            # 各科目グループを処理
            for course_data in course_groups:
                course_code = course_data['course_code']
                course_name = course_data['course_name']
                instructor_name = course_data['instructor']
                day_of_week = course_data['day_of_week']
                start_period = course_data['start_period']
                length = course_data['length']

                # 講師を挿入または取得
                instructor_id = await self._get_or_create_instructor(instructor_name)

                # 科目を挿入または更新
                await self._insert_or_update_course(
                    course_code, course_name, instructor_id, start_period, day_of_week, length
                )

                # 受講情報を挿入
                await self._insert_attendance(db_user_id, course_code)

            logging.info(f"✅ Tursoデータベースに保存完了: {len(course_groups)}件の科目")
            return True

        except Exception as e:
            logging.error(f"❌ Tursoデータベース保存エラー: {e}")
            return False
    
    async def get_latest_courses(self, user_id: str) -> List[Dict]:
        """
        指定ユーザーの履修科目を取得（table3.md構造版）

        Args:
            user_id (str): ユーザーID

        Returns:
            List[Dict]: 履修科目のリスト
        """
        try:
            result = await self.client.execute(
                """
                SELECT DISTINCT c.course_id, c.course_name, i.instructor,
                       c.day_of_week, c.period, c.length
                FROM user u
                JOIN attendance a ON u.user_id = a.user_id
                JOIN courses c ON a.course_id = c.course_id
                JOIN instructor i ON c.instructor_id = i.instructor_id
                WHERE u.login_id = ?
                ORDER BY c.day_of_week, c.period
                """,
                [user_id]
            )

            courses = []
            for row in result.rows:
                courses.append({
                    'course_code': row[0],
                    'course_name': row[1],
                    'instructor': row[2],
                    'day_of_week': row[3],
                    'period': row[4],
                    'length': row[5]
                })

            return courses

        except Exception as e:
            logging.error(f"❌ Tursoデータ取得エラー: {e}")
            return []
    
    async def get_database_stats(self) -> Dict:
        """
        データベースの統計情報を取得（table3.md構造版）

        Returns:
            Dict: 統計情報
        """
        try:
            # セッション数
            session_result = await self.client.execute("SELECT COUNT(*) FROM scraping_sessions")
            session_count = session_result.rows[0][0] if session_result.rows else 0

            # 科目数
            course_result = await self.client.execute("SELECT COUNT(*) FROM courses")
            course_count = course_result.rows[0][0] if course_result.rows else 0

            # 講師数
            instructor_result = await self.client.execute("SELECT COUNT(*) FROM instructor")
            instructor_count = instructor_result.rows[0][0] if instructor_result.rows else 0

            # ユーザー数
            user_result = await self.client.execute("SELECT COUNT(*) FROM user")
            user_count = user_result.rows[0][0] if user_result.rows else 0

            # 受講登録数
            attendance_result = await self.client.execute("SELECT COUNT(*) FROM attendance")
            attendance_count = attendance_result.rows[0][0] if attendance_result.rows else 0

            # 最新セッション
            latest_result = await self.client.execute(
                """
                SELECT user_id, timestamp, total_elements
                FROM scraping_sessions
                ORDER BY created_at DESC
                LIMIT 1
                """
            )

            latest_session = None
            if latest_result.rows:
                row = latest_result.rows[0]
                latest_session = {
                    'user_id': row[0],
                    'timestamp': row[1],
                    'total_elements': row[2]
                }

            return {
                'session_count': session_count,
                'course_count': course_count,
                'instructor_count': instructor_count,
                'user_count': user_count,
                'attendance_count': attendance_count,
                'latest_session': latest_session
            }

        except Exception as e:
            logging.error(f"❌ Turso統計情報取得エラー: {e}")
            return {}
    
    async def close(self):
        """データベース接続を閉じる"""
        if self.client:
            await self.client.close()
            logging.info("✅ Tursoデータベース接続を閉じました")


# 同期実行用のヘルパー関数
def save_to_turso_sync(json_data: Dict) -> bool:
    """同期実行でTursoデータベースに保存"""
    async def _save():
        db = TursoDatabase()
        try:
            await db.init_database()
            return await db.save_json_data(json_data)
        finally:
            await db.close()
    
    try:
        return asyncio.run(_save())
    except Exception as e:
        logging.error(f"❌ Turso同期保存エラー: {e}")
        return False


def get_latest_courses_sync(user_id: str) -> List[Dict]:
    """同期実行で最新の履修科目を取得"""
    async def _get():
        db = TursoDatabase()
        try:
            await db.init_database()
            return await db.get_latest_courses(user_id)
        finally:
            await db.close()
    
    try:
        return asyncio.run(_get())
    except Exception as e:
        logging.error(f"❌ Tursoデータ取得エラー: {e}")
        return []


def get_database_stats_sync() -> Dict:
    """同期実行でデータベース統計情報を取得"""
    async def _get():
        db = TursoDatabase()
        try:
            await db.init_database()
            return await db.get_database_stats()
        finally:
            await db.close()
    
    try:
        return asyncio.run(_get())
    except Exception as e:
        logging.error(f"❌ Turso統計情報取得エラー: {e}")
        return {}
