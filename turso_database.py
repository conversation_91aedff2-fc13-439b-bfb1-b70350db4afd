#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Turso/LibSQLデータベース操作モジュール（シンプル版）
元の動作していた状態に戻したバージョン
"""

import os
import json
import logging
import asyncio
from typing import Dict, List, Optional
from datetime import datetime
from dotenv import load_dotenv
import libsql_client

# .envファイルから環境変数を読み込み
load_dotenv()


class TursoDatabase:
    """Turso/LibSQLデータベース操作クラス（シンプル版）"""
    
    def __init__(self):
        """
        Tursoデータベース接続を初期化
        """
        self.database_url = os.getenv("DATABASE_URL")
        self.database_token = os.getenv("DATABASE_TOKEN")
        
        if not self.database_url or not self.database_token:
            raise ValueError("DATABASE_URLとDATABASE_TOKENが.envファイルに設定されていません")
        
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Tursoクライアントを初期化"""
        try:
            # HTTPSプロトコルを使用
            if self.database_url.startswith("libsql://"):
                https_url = self.database_url.replace("libsql://", "https://")
            else:
                https_url = self.database_url

            self.client = libsql_client.create_client(
                url=https_url,
                auth_token=self.database_token
            )
            
            logging.info("✅ Tursoデータベースクライアントを初期化しました")
        except Exception as e:
            logging.error(f"❌ Tursoクライアント初期化エラー: {e}")
            raise
    
    async def init_database(self):
        """
        データベーステーブルを初期化（table2.md構造版）
        """
        try:
            # 講師テーブル（instructor）
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS instructor (
                    instructor_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    instructor TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 科目テーブル（courses）- course_idとperiodの複合主キー
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS courses (
                    course_id TEXT NOT NULL,
                    period TEXT NOT NULL,
                    course_name TEXT NOT NULL,
                    instructor_id INTEGER NOT NULL,
                    day_of_week TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (course_id, period),
                    FOREIGN KEY (instructor_id) REFERENCES instructor(instructor_id)
                )
            """)

            # ユーザー履修テーブル（user）
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS user (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    login_id TEXT NOT NULL,
                    course_id TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (course_id) REFERENCES courses(course_id)
                )
            """)

            # セッション履歴テーブル（スクレイピング履歴用）
            await self.client.execute("""
                CREATE TABLE IF NOT EXISTS scraping_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    page_title TEXT,
                    target_url TEXT,
                    extract_class TEXT,
                    total_elements INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            logging.info("✅ Tursoデータベーステーブルを初期化しました（table2.md構造）")
        except Exception as e:
            logging.error(f"❌ Tursoデータベース初期化エラー: {e}")
            raise
    
    async def save_json_data(self, json_data: Dict) -> bool:
        """
        JSONデータをTursoデータベースに保存（table2.md構造版）

        Args:
            json_data (Dict): 保存するJSONデータ

        Returns:
            bool: 保存成功時True
        """
        try:
            # セッション情報を挿入
            await self.client.execute(
                """
                INSERT INTO scraping_sessions
                (user_id, timestamp, page_title, target_url, extract_class, total_elements)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                [
                    json_data.get('user_id', 'unknown'),
                    json_data.get('timestamp'),
                    json_data.get('page_title'),
                    json_data.get('target_url'),
                    json_data.get('extract_class'),
                    json_data.get('total_elements', 0)
                ]
            )

            user_id = json_data.get('user_id', 'unknown')

            # 科目情報を正規化して保存
            for element in json_data.get('elements', []):
                course_info = element.get('course_info', {})
                delete_params = element.get('delete_params', {})

                course_id = course_info.get('course_code')  # course_idとして使用
                course_name = course_info.get('course_name')
                instructor_name = course_info.get('instructor')
                day_of_week = delete_params.get('param1')  # 曜日
                period = delete_params.get('param2')       # 時限

                if not course_id or not course_name or not instructor_name:
                    continue

                # 1. 講師を instructor テーブルに挿入/取得
                try:
                    instructor_result = await self.client.execute(
                        "SELECT instructor_id FROM instructor WHERE instructor = ?",
                        [instructor_name]
                    )

                    if instructor_result.rows:
                        instructor_id = instructor_result.rows[0][0]
                        logging.debug(f"🔍 既存講師 {instructor_name} を使用: ID={instructor_id}")
                    else:
                        # 新規講師を挿入
                        new_instructor = await self.client.execute(
                            "INSERT INTO instructor (instructor) VALUES (?)",
                            [instructor_name]
                        )
                        instructor_id = new_instructor.last_insert_rowid
                        logging.info(f"👨‍🏫 新規講師 {instructor_name} を追加しました: ID={instructor_id}")
                except Exception as instructor_error:
                    logging.error(f"❌ 講師処理エラー: {instructor_error}")
                    continue

                # 2. 科目を courses テーブルに挿入/更新（複合主キーで重複確認）
                try:
                    course_result = await self.client.execute(
                        "SELECT course_id, period FROM courses WHERE course_id = ? AND period = ?",
                        [course_id, period]
                    )

                    if course_result.rows:
                        # 既存科目を更新
                        await self.client.execute(
                            """
                            UPDATE courses SET
                            course_name = ?, instructor_id = ?, day_of_week = ?
                            WHERE course_id = ? AND period = ?
                            """,
                            [course_name, instructor_id, day_of_week, course_id, period]
                        )
                        logging.info(f"🔄 科目 {course_id}({period}時限) を更新しました")
                    else:
                        # 新規科目を挿入
                        await self.client.execute(
                            """
                            INSERT INTO courses (course_id, period, course_name, instructor_id, day_of_week)
                            VALUES (?, ?, ?, ?, ?)
                            """,
                            [course_id, period, course_name, instructor_id, day_of_week]
                        )
                        logging.info(f"📚 新規科目 {course_id}({period}時限) を追加しました")
                except Exception as course_error:
                    logging.error(f"❌ 科目処理エラー: {course_error}")
                    continue

                # 3. ユーザー履修情報を user テーブルに挿入
                # 注意: userテーブルのcourse_idは科目コードのみを参照（時限は含まない）
                try:
                    user_course_result = await self.client.execute(
                        "SELECT id FROM user WHERE login_id = ? AND course_id = ?",
                        [user_id, course_id]
                    )

                    if not user_course_result.rows:
                        # 新規履修情報を挿入
                        await self.client.execute(
                            "INSERT INTO user (login_id, course_id) VALUES (?, ?)",
                            [user_id, course_id]
                        )
                        logging.info(f"👤 ユーザー {user_id} の履修科目 {course_id} を追加しました")
                    else:
                        logging.debug(f"🔍 既存履修情報: {user_id} - {course_id}")
                except Exception as user_error:
                    logging.error(f"❌ ユーザー履修処理エラー: {user_error}")
                    continue

            logging.info(f"✅ Tursoデータベースに保存しました: {len(json_data.get('elements', []))}件の科目")
            return True

        except Exception as e:
            logging.error(f"❌ Tursoデータベース保存エラー: {e}")
            return False
    
    async def get_latest_courses(self, user_id: str) -> List[Dict]:
        """
        指定ユーザーの履修科目を取得（table2.md構造版）

        Args:
            user_id (str): ユーザーID

        Returns:
            List[Dict]: 履修科目のリスト
        """
        try:
            result = await self.client.execute(
                """
                SELECT c.course_id, c.period, c.course_name, i.instructor, c.day_of_week
                FROM user u
                JOIN courses c ON u.course_id = c.course_id
                JOIN instructor i ON c.instructor_id = i.instructor_id
                WHERE u.login_id = ?
                ORDER BY u.created_at DESC, c.period
                """,
                [user_id]
            )

            courses = []
            for row in result.rows:
                courses.append({
                    'course_code': row[0],
                    'period': row[1],
                    'course_name': row[2],
                    'instructor': row[3],
                    'day_of_week': row[4]
                })

            return courses

        except Exception as e:
            logging.error(f"❌ Tursoデータ取得エラー: {e}")
            return []
    
    async def get_database_stats(self) -> Dict:
        """
        データベースの統計情報を取得（table2.md構造版）

        Returns:
            Dict: 統計情報
        """
        try:
            # セッション数
            session_result = await self.client.execute("SELECT COUNT(*) FROM scraping_sessions")
            session_count = session_result.rows[0][0] if session_result.rows else 0

            # 科目数
            course_result = await self.client.execute("SELECT COUNT(*) FROM courses")
            course_count = course_result.rows[0][0] if course_result.rows else 0

            # 講師数
            instructor_result = await self.client.execute("SELECT COUNT(*) FROM instructor")
            instructor_count = instructor_result.rows[0][0] if instructor_result.rows else 0

            # ユーザー数
            user_result = await self.client.execute("SELECT COUNT(DISTINCT login_id) FROM user")
            user_count = user_result.rows[0][0] if user_result.rows else 0

            # 履修登録数
            enrollment_result = await self.client.execute("SELECT COUNT(*) FROM user")
            enrollment_count = enrollment_result.rows[0][0] if enrollment_result.rows else 0

            # 最新セッション
            latest_result = await self.client.execute(
                """
                SELECT user_id, timestamp, total_elements
                FROM scraping_sessions
                ORDER BY created_at DESC
                LIMIT 1
                """
            )

            latest_session = None
            if latest_result.rows:
                row = latest_result.rows[0]
                latest_session = {
                    'user_id': row[0],
                    'timestamp': row[1],
                    'total_elements': row[2]
                }

            return {
                'session_count': session_count,
                'course_count': course_count,
                'instructor_count': instructor_count,
                'user_count': user_count,
                'enrollment_count': enrollment_count,
                'latest_session': latest_session
            }

        except Exception as e:
            logging.error(f"❌ Turso統計情報取得エラー: {e}")
            return {}
    
    async def close(self):
        """データベース接続を閉じる"""
        if self.client:
            await self.client.close()
            logging.info("✅ Tursoデータベース接続を閉じました")


# 同期実行用のヘルパー関数
def save_to_turso_sync(json_data: Dict) -> bool:
    """同期実行でTursoデータベースに保存"""
    async def _save():
        db = TursoDatabase()
        try:
            await db.init_database()
            return await db.save_json_data(json_data)
        finally:
            await db.close()
    
    try:
        return asyncio.run(_save())
    except Exception as e:
        logging.error(f"❌ Turso同期保存エラー: {e}")
        return False


def get_latest_courses_sync(user_id: str) -> List[Dict]:
    """同期実行で最新の履修科目を取得"""
    async def _get():
        db = TursoDatabase()
        try:
            await db.init_database()
            return await db.get_latest_courses(user_id)
        finally:
            await db.close()
    
    try:
        return asyncio.run(_get())
    except Exception as e:
        logging.error(f"❌ Tursoデータ取得エラー: {e}")
        return []


def get_database_stats_sync() -> Dict:
    """同期実行でデータベース統計情報を取得"""
    async def _get():
        db = TursoDatabase()
        try:
            await db.init_database()
            return await db.get_database_stats()
        finally:
            await db.close()
    
    try:
        return asyncio.run(_get())
    except Exception as e:
        logging.error(f"❌ Turso統計情報取得エラー: {e}")
        return {}
