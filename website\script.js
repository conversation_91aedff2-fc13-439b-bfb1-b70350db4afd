// Vercel API エンドポイント（デプロイ後に実際のURLに変更してください）
const API_ENDPOINT = '/api/scrape';

document.getElementById('scrapingForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('submitBtn');
    const loading = document.getElementById('loading');
    const result = document.getElementById('result');
    
    // UI状態を更新
    submitBtn.disabled = true;
    submitBtn.textContent = '処理中...';
    loading.style.display = 'block';
    result.style.display = 'none';
    
    try {
        const formData = new FormData(this);

        // URLエンコード形式に変換
        const urlEncodedData = new URLSearchParams();
        urlEncodedData.append('login_id', formData.get('login_id'));
        urlEncodedData.append('password', formData.get('password'));

        // デバッグ用ログ
        console.log('送信データ:', {
            login_id: formData.get('login_id'),
            password: formData.get('password') ? '***' : 'empty'
        });

        const response = await fetch(API_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: urlEncodedData
        });

        console.log('レスポンスステータス:', response.status);

        let data;
        try {
            data = await response.json();
            console.log('レスポンスデータ:', data);
        } catch (jsonError) {
            console.error('JSON解析エラー:', jsonError);
            const text = await response.text();
            console.log('レスポンステキスト:', text);
            throw new Error(`JSON解析に失敗しました: ${text.substring(0, 100)}`);
        }

        if (response.ok && data.success) {
            result.className = 'result success';
            result.innerHTML = `
                <h3>✅ 処理完了！</h3>
                <p><strong>ページタイトル:</strong> ${data.title}</p>
                <p><strong>抽出要素数:</strong> ${data.extracted_count}件</p>
                <p><strong>データベース保存:</strong> ${data.db_saved ? '成功' : '失敗'}</p>
            `;
        } else {
            result.className = 'result error';
            result.innerHTML = `
                <h3>❌ エラーが発生しました</h3>
                <p><strong>ステータス:</strong> ${response.status}</p>
                <p><strong>エラー:</strong> ${data.error || 'Unknown error'}</p>
                <details>
                    <summary>詳細情報</summary>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </details>
            `;
        }
    } catch (error) {
        console.error('通信エラー:', error);
        result.className = 'result error';
        result.innerHTML = `
            <h3>❌ 通信エラー</h3>
            <p>サーバーとの通信に失敗しました: ${error.message}</p>
            <p>詳細はブラウザのコンソールを確認してください。</p>
        `;
    }
    
    // UI状態をリセット
    submitBtn.disabled = false;
    submitBtn.textContent = '📚 履修情報を取得';
    loading.style.display = 'none';
    result.style.display = 'block';
});
